"use client"

import { useState, useEffect } from "react";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import CodeEditor from "@/components/code-editor";

interface FileData {
  id: string;
  name: string;
  type: string;
  path: string;
  content?: string;
}

export default function EditorWindowPage() {
  const [selectedFile, setSelectedFile] = useState<FileData | null>(null);
  const [theme, setTheme] = useState<string>("dark");

  useEffect(() => {
    // Get file path from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const filePath = urlParams.get('file');
    
    if (filePath) {
      // Create a file object from the path
      const fileName = filePath.split('/').pop() || 'Unknown File';
      const fileExtension = fileName.split('.').pop() || 'txt';
      
      const fileData: FileData = {
        id: Date.now().toString(),
        name: fileName,
        type: fileExtension,
        path: filePath
      };
      
      setSelectedFile(fileData);
    }
  }, []);

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="h-screen w-full bg-background text-foreground">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <h1 className="text-lg font-semibold">
              {selectedFile ? selectedFile.name : "Code Editor"}
            </h1>
            {selectedFile && (
              <span className="text-sm text-muted-foreground">
                {selectedFile.path}
              </span>
            )}
          </div>
          
          {/* Editor Content */}
          <div className="flex-1 overflow-hidden">
            <CodeEditor file={selectedFile} appTheme={theme} />
          </div>
        </div>
        <Toaster />
      </div>
    </ThemeProvider>
  );
}
