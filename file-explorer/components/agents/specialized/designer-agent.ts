// components/agents/specialized/designer-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';

export class DesignerAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'ui_design',
      'component_styling',
      'responsive_design',
      'accessibility',
      'brand_consistency',
      'interaction_design',
      'visual_optimization',
      'css_architecture'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Designer agent, responsible for UI/UX implementation, styling, and visual coherence.

CORE RESPONSIBILITIES:
1. Create visually appealing, user-friendly interfaces
2. Maintain visual consistency with existing components
3. Follow accessibility standards (WCAG)
4. Ensure responsive behavior across devices
5. Design for reusability and extensibility

Focus on creating designs that are both visually appealing and functionally effective.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      const design = await this.createDesign(context);
      
      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 600;

      return this.createSuccessResponse(
        design,
        tokensUsed,
        executionTime,
        ['Ensure accessibility compliance', 'Test responsive behavior'],
        { taskType: 'ui_design' }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Design creation failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private async createDesign(context: AgentContext): Promise<string> {
    return `// UI Design Implementation
// Task: ${context.task}

import React from 'react';
import { cn } from '@/lib/utils';

interface DesignComponentProps {
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children?: React.ReactNode;
}

export const DesignComponent: React.FC<DesignComponentProps> = ({
  className,
  variant = 'primary',
  size = 'md',
  children,
  ...props
}) => {
  return (
    <div 
      className={cn(
        'rounded-md transition-all duration-200',
        {
          'bg-primary text-primary-foreground': variant === 'primary',
          'bg-secondary text-secondary-foreground': variant === 'secondary',
          'border border-input bg-background': variant === 'outline',
        },
        {
          'px-2 py-1 text-sm': size === 'sm',
          'px-4 py-2 text-base': size === 'md',
          'px-6 py-3 text-lg': size === 'lg',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// Responsive styles
const styles = \`
  @media (max-width: 768px) {
    .design-component {
      padding: 0.5rem;
    }
  }
\`;

export default DesignComponent;
`;
  }
}
