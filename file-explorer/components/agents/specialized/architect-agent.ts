// components/agents/specialized/architect-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';

export class ArchitectAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'system_design',
      'component_architecture',
      'technical_strategy',
      'design_patterns',
      'scalability_planning',
      'integration_design',
      'technology_evaluation',
      'architectural_documentation'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Architect agent, responsible for high-level system design, component relationships, and technical strategy.

CORE RESPONSIBILITIES:
1. Create coherent system designs balancing technical excellence with practical implementation
2. Design clear component boundaries and interfaces
3. Choose appropriate design patterns and document decisions
4. Create hierarchical task trees with clear dependencies
5. Establish architectural validation criteria and quality standards

Focus on creating designs that are both technically sound and practically implementable.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      const architecture = await this.designArchitecture(context);
      
      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 800;

      return this.createSuccessResponse(
        architecture,
        tokensUsed,
        executionTime,
        ['Review implementation feasibility', 'Consider scalability requirements'],
        { taskType: 'architectural_design' }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Architecture design failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private async designArchitecture(context: AgentContext): Promise<string> {
    return `// Architectural Design
// Task: ${context.task}

/**
 * SYSTEM ARCHITECTURE SPECIFICATION
 */

## Component Architecture
- Core Components: [List main components]
- Integration Points: [Define interfaces]
- Data Flow: [Describe data movement]

## Design Patterns
- Applied Patterns: MVC, Repository, Observer
- Rationale: [Explain pattern choices]

## Quality Attributes
- Scalability: Horizontal scaling support
- Performance: < 200ms response time
- Security: Authentication/Authorization layers
- Maintainability: Modular design

## Implementation Guidelines
1. Follow established patterns
2. Implement proper error handling
3. Add comprehensive logging
4. Include performance monitoring

export default architecturalSpecification;
`;
  }
}
