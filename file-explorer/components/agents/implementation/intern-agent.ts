// components/agents/implementation/intern-agent.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from '../agent-base';

export class InternAgent extends AgentBase {
  constructor(config: AgentConfig) {
    super(config);
  }

  public getCapabilities(): string[] {
    return [
      'simple_tasks',
      'boilerplate_generation',
      'template_implementation',
      'basic_file_operations',
      'simple_debugging'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Intern implementation agent, responsible for handling simple, well-defined coding tasks.

CORE RESPONSIBILITIES:
1. Implement straightforward, clearly specified code following explicit instructions
2. Focus only on the context provided for your specific task
3. Try one straightforward solution, report issues if unsuccessful
4. Stick strictly to simple, well-defined tasks

CAPABILITIES:
- Single file, template-based tasks
- Boilerplate code generation
- Simple function implementations
- Basic file operations
- Following explicit patterns

ESCALATION CRITERIA:
- Complex algorithms or logic
- Multiple file interactions
- Architectural decisions
- Performance optimization

Focus on accuracy and consistency rather than innovation.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      // Check if task is suitable for intern level
      if (!this.isTaskSuitableForIntern(context.task)) {
        return this.createErrorResponse(
          'Task complexity exceeds Intern capabilities. Consider Junior or higher level agent.'
        );
      }

      // Generate simple implementation
      const implementation = this.generateSimpleImplementation(context);
      
      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 200;

      return this.createSuccessResponse(
        implementation,
        tokensUsed,
        executionTime,
        ['Task completed with basic implementation', 'Consider review by higher-level agent'],
        { taskType: 'simple_implementation' }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Intern task failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  private isTaskSuitableForIntern(task: string): boolean {
    const task_lower = task.toLowerCase();
    
    // Tasks too complex for intern
    const complexIndicators = [
      'complex', 'algorithm', 'architecture', 'performance', 'optimization',
      'multiple files', 'integration', 'database', 'api design'
    ];

    return !complexIndicators.some(indicator => task_lower.includes(indicator));
  }

  private generateSimpleImplementation(context: AgentContext): string {
    return `// Simple implementation generated by Intern Agent
// Task: ${context.task}

// TODO: Implement the following based on requirements:
export function simpleFunction() {
  // Basic implementation
  return 'Task completed';
}

export default simpleFunction;
`;
  }
}
