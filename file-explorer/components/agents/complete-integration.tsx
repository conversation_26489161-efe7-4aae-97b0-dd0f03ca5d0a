// Integration Guide: Complete Agent System Setup
// File: components/agents/complete-integration.tsx

import React, { useState, useEffect } from 'react';
import { CompleteAgentManager } from './agent-manager-complete';
import { AgentIntegration } from './agent-integration';
import { SettingsManager } from '../settings/settings-manager';
import { SettingsUI } from '../settings/settings-ui';
import { SharedAgentStateProvider, useSharedAgentState } from './shared-agent-state';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, CheckCircle, Settings, Activity, BarChart } from 'lucide-react';

interface CompleteSystemProps {
  className?: string;
}

// Wrapper component that provides shared state
export const CompleteAgentSystem: React.FC<CompleteSystemProps> = ({ className }) => {
  return (
    <SharedAgentStateProvider>
      <CompleteAgentSystemInner className={className} />
    </SharedAgentStateProvider>
  );
};

// Inner component that uses shared state
const CompleteAgentSystemInner: React.FC<CompleteSystemProps> = ({ className }) => {
  const sharedState = useSharedAgentState();
  const [agentManager] = useState(() => new CompleteAgentManager());
  const [settingsManager] = useState(() => new SettingsManager());
  const [showSettings, setShowSettings] = useState(false);

  // Derive system metrics from shared state
  const systemMetrics = {
    systemHealthScore: sharedState.agents.reduce((acc, agent) => acc + agent.healthScore, 0) / sharedState.agents.length || 0,
    activeAgents: sharedState.agents.filter(agent => agent.status === 'busy').length,
    queueLength: sharedState.tasks.filter(task => task.status === 'pending').length,
    totalTasks: sharedState.tasks.length,
    successfulTasks: sharedState.tasks.filter(task => task.status === 'completed').length,
    averageResponseTime: 2000, // Mock value
    totalTokensUsed: sharedState.agents.reduce((acc, agent) => acc + agent.tokensUsed, 0)
  };

  // Mock optimizations for now
  const optimizations = agentManager.getOptimizationSuggestions ? agentManager.getOptimizationSuggestions() : [];

  useEffect(() => {
    // Listen for system messages
    const handleMessage = (message: any) => {
      console.log('System message:', message);
      // Add message to shared state
      sharedState.addMessage({
        agentId: message.agentId || 'system',
        message: message.message || message.toString(),
        timestamp: Date.now(),
        type: message.type || 'info'
      });
    };

    agentManager.onMessage(handleMessage);

    return () => {
      agentManager.offMessage(handleMessage);
    };
  }, [agentManager, sharedState]);

  const handleTaskSubmission = async (task: string) => {
    try {
      // Add task to shared state
      await sharedState.assignTask({
        agentId: sharedState.selectedAgent,
        description: task,
        status: 'pending',
        priority: 'medium'
      });

      const taskId = await agentManager.submitTask(task);
      console.log(`Task submitted with ID: ${taskId}`);
      return taskId;
    } catch (error) {
      console.error('Task submission failed:', error);
      throw error;
    }
  };

  const getSystemHealthColor = (health: number) => {
    if (health >= 80) return 'text-green-500';
    if (health >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getSystemStatusIcon = (health: number) => {
    if (health >= 80) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (health >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    return <AlertTriangle className="h-5 w-5 text-red-500" />;
  };

  return (
    <div className={`h-full bg-background ${className}`}>
      <div className="flex flex-col h-full">
        {/* System Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                {getSystemStatusIcon(systemMetrics.systemHealthScore)}
                <h1 className="text-2xl font-bold">Agent System</h1>
                <Badge variant={systemMetrics.systemHealthScore >= 80 ? 'default' : 'destructive'}>
                  {systemMetrics.systemHealthScore.toFixed(1)}% Health
                </Badge>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>{systemMetrics.activeAgents} Active Agents</span>
                <span>{systemMetrics.queueLength} Queued</span>
                <span>{systemMetrics.totalTasks} Total Tasks</span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          {showSettings ? (
            <SettingsUI
              settingsManager={settingsManager}
              onClose={() => setShowSettings(false)}
            />
          ) : (
            <Tabs defaultValue="agents" className="h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-5 mx-4 mt-4">
                <TabsTrigger value="agents">Agents</TabsTrigger>
                <TabsTrigger value="tasks">Tasks</TabsTrigger>
                <TabsTrigger value="metrics">Metrics</TabsTrigger>
                <TabsTrigger value="optimization">Optimization</TabsTrigger>
                <TabsTrigger value="system">System</TabsTrigger>
              </TabsList>

              <TabsContent value="agents" className="flex-1 p-4 overflow-auto">
                <AgentIntegration />
              </TabsContent>

              <TabsContent value="tasks" className="flex-1 p-4 overflow-auto">
                <TaskManagementPanel />
              </TabsContent>

              <TabsContent value="metrics" className="flex-1 p-4 overflow-auto">
                <MetricsPanel
                  systemMetrics={systemMetrics}
                  agentStatuses={sharedState.agents}
                  agentManager={agentManager}
                />
              </TabsContent>

              <TabsContent value="optimization" className="flex-1 p-4 overflow-auto">
                <OptimizationPanel
                  optimizations={optimizations}
                  agentManager={agentManager}
                />
              </TabsContent>

              <TabsContent value="system" className="flex-1 p-4 overflow-auto">
                <SystemPanel agentManager={agentManager} />
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </div>
  );
};

// Task Management Panel Component
const TaskManagementPanel: React.FC = () => {
  const sharedState = useSharedAgentState();

  const activeTasks = sharedState.tasks.filter(task => task.status === 'running');
  const queuedTasks = sharedState.tasks.filter(task => task.status === 'pending');
  const taskHistory = sharedState.tasks.filter(task => task.status === 'completed' || task.status === 'failed').slice(-20);

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Active Tasks</CardTitle>
            <CardDescription>{activeTasks.length} currently executing</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {activeTasks.slice(0, 5).map(task => (
                <div key={task.id} className="p-2 border rounded-md">
                  <div className="font-medium text-sm truncate">{task.description}</div>
                  <div className="text-xs text-muted-foreground">
                    Agent: {task.agentId} | Priority: {task.priority}
                  </div>
                </div>
              ))}
              {activeTasks.length === 0 && (
                <p className="text-sm text-muted-foreground">No active tasks</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Queue</CardTitle>
            <CardDescription>{queuedTasks.length} waiting</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {queuedTasks.slice(0, 5).map(task => (
                <div key={task.id} className="p-2 border rounded-md">
                  <div className="font-medium text-sm truncate">{task.description}</div>
                  <div className="text-xs text-muted-foreground">
                    Agent: {task.agentId} | Priority: {task.priority}
                  </div>
                </div>
              ))}
              {queuedTasks.length === 0 && (
                <p className="text-sm text-muted-foreground">No queued tasks</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Recent History</CardTitle>
            <CardDescription>Last {taskHistory.length} completed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {taskHistory.slice(0, 5).map(task => (
                <div key={task.id} className="p-2 border rounded-md">
                  <div className="font-medium text-sm truncate">{task.description}</div>
                  <div className="text-xs text-muted-foreground flex items-center justify-between">
                    <span>Agent: {task.agentId}</span>
                    <Badge variant={task.status === 'completed' ? 'default' : 'destructive'}>
                      {task.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Metrics Panel Component
const MetricsPanel: React.FC<{
  systemMetrics: any;
  agentStatuses: any[];
  agentManager: CompleteAgentManager;
}> = ({ systemMetrics, agentStatuses, agentManager }) => {
  const successRate = systemMetrics.totalTasks > 0 ?
    (systemMetrics.successfulTasks / systemMetrics.totalTasks) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* System Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart className="h-5 w-5" />
            System Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <div className="text-sm font-medium">Success Rate</div>
              <div className="text-2xl font-bold text-green-600">{successRate.toFixed(1)}%</div>
              <Progress value={successRate} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Average Response Time</div>
              <div className="text-2xl font-bold">{(systemMetrics.averageResponseTime / 1000).toFixed(1)}s</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">Total Tokens</div>
              <div className="text-2xl font-bold">{systemMetrics.totalTokensUsed.toLocaleString()}</div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium">System Health</div>
              <div className="text-2xl font-bold text-blue-600">{systemMetrics.systemHealthScore.toFixed(1)}%</div>
              <Progress value={systemMetrics.systemHealthScore} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Agent Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Agent Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {agentStatuses.map(agent => (
              <div key={agent.id} className="p-4 border rounded-md">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{agent.name}</span>
                  <Badge variant={agent.healthScore >= 70 ? 'default' : 'destructive'}>
                    {agent.healthScore.toFixed(0)}%
                  </Badge>
                </div>
                <Progress value={agent.healthScore} className="h-2 mb-2" />
                <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                  <div>Tasks: {agent.tasksCompleted}</div>
                  <div>Errors: {agent.errorCount}</div>
                  <div>Tokens: {agent.tokensUsed.toLocaleString()}</div>
                  <div>Status: {agent.status}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Optimization Panel Component
const OptimizationPanel: React.FC<{
  optimizations: any[];
  agentManager: CompleteAgentManager;
}> = ({ optimizations, agentManager }) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Optimization Suggestions</CardTitle>
          <CardDescription>AI-generated recommendations to improve system performance</CardDescription>
        </CardHeader>
        <CardContent>
          {optimizations.length > 0 ? (
            <div className="space-y-4">
              {optimizations.map(opt => (
                <div key={opt.id} className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={opt.priority === 'high' ? 'destructive' : opt.priority === 'medium' ? 'default' : 'secondary'}>
                        {opt.priority}
                      </Badge>
                      <span className="font-medium">{opt.targetAgent}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Impact: {(opt.expectedImpact * 100).toFixed(0)}% | Effort: {opt.effort}/10
                    </div>
                  </div>
                  <p className="text-sm">{opt.description}</p>
                  {opt.data?.suggestions && (
                    <div className="mt-2">
                      <div className="text-xs font-medium text-muted-foreground mb-1">Suggestions:</div>
                      <ul className="text-xs text-muted-foreground list-disc list-inside">
                        {opt.data.suggestions.map((suggestion: string, index: number) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">No optimization suggestions available</p>
          )}
        </CardContent>
      </Card>

      {/* Learning Patterns */}
      <Card>
        <CardHeader>
          <CardTitle>Learning Patterns</CardTitle>
          <CardDescription>Patterns identified by the learning system</CardDescription>
        </CardHeader>
        <CardContent>
          <LearningPatternsDisplay agentManager={agentManager} />
        </CardContent>
      </Card>
    </div>
  );
};

// Learning Patterns Display Component
const LearningPatternsDisplay: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const [patterns, setPatterns] = useState(agentManager.getLearningPatterns());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    const interval = setInterval(() => {
      setPatterns(agentManager.getLearningPatterns());
    }, 10000);

    return () => clearInterval(interval);
  }, [agentManager]);

  const categories = ['all', 'success', 'failure', 'optimization', 'error_resolution'];
  const filteredPatterns = selectedCategory === 'all' ?
    patterns : patterns.filter(p => p.category === selectedCategory);

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category)}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </Button>
        ))}
      </div>

      <div className="space-y-3 max-h-64 overflow-auto">
        {filteredPatterns.slice(0, 10).map(pattern => (
          <div key={pattern.id} className="p-3 border rounded-md">
            <div className="flex items-center justify-between mb-1">
              <Badge variant={
                pattern.category === 'success' ? 'default' :
                pattern.category === 'failure' ? 'destructive' :
                'secondary'
              }>
                {pattern.category}
              </Badge>
              <div className="text-xs text-muted-foreground">
                Frequency: {pattern.frequency} | Effectiveness: {(pattern.effectiveness * 100).toFixed(0)}%
              </div>
            </div>
            <p className="text-sm font-medium mb-1">{pattern.pattern}</p>
            {pattern.recommendations.length > 0 && (
              <div className="text-xs text-muted-foreground">
                <strong>Recommendations:</strong> {pattern.recommendations.slice(0, 2).join(', ')}
              </div>
            )}
          </div>
        ))}
        {filteredPatterns.length === 0 && (
          <p className="text-sm text-muted-foreground">No patterns found for this category</p>
        )}
      </div>
    </div>
  );
};

// System Panel Component
const SystemPanel: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const [systemReport, setSystemReport] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateReport = async () => {
    setIsGenerating(true);
    try {
      const report = await agentManager.generateSystemReport();
      setSystemReport(report);
    } catch (error) {
      console.error('Failed to generate system report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>Overall system health and diagnostics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={generateReport} disabled={isGenerating}>
              {isGenerating ? 'Generating...' : 'Generate System Report'}
            </Button>

            {systemReport && (
              <div className="bg-muted p-4 rounded-md">
                <pre className="text-sm whitespace-pre-wrap font-mono">{systemReport}</pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* System Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>System Configuration</CardTitle>
          <CardDescription>Current system configuration and capabilities</CardDescription>
        </CardHeader>
        <CardContent>
          <SystemConfigurationDisplay agentManager={agentManager} />
        </CardContent>
      </Card>
    </div>
  );
};

// System Configuration Display Component
const SystemConfigurationDisplay: React.FC<{ agentManager: CompleteAgentManager }> = ({ agentManager }) => {
  const agents = agentManager.getAgents();
  const agentsByType = agents.reduce((acc, agent) => {
    const type = agent.getType();
    if (!acc[type]) acc[type] = [];
    acc[type].push(agent);
    return acc;
  }, {} as Record<string, any[]>);

  return (
    <div className="space-y-4">
      {Object.entries(agentsByType).map(([type, typeAgents]) => (
        <div key={type} className="space-y-2">
          <h3 className="font-medium capitalize">{type} Agents ({typeAgents.length})</h3>
          <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
            {typeAgents.map(agent => (
              <div key={agent.getId()} className="p-3 border rounded-md">
                <div className="font-medium text-sm">{agent.getName()}</div>
                <div className="text-xs text-muted-foreground mt-1">
                  Capabilities: {agent.getCapabilities().slice(0, 3).join(', ')}
                  {agent.getCapabilities().length > 3 && ` +${agent.getCapabilities().length - 3} more`}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

// Integration Instructions Component
export const IntegrationInstructions: React.FC = () => {
  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>🚀 Agent System Integration Instructions</CardTitle>
        <CardDescription>How to integrate the complete agent system into your application</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">📋 Integration Checklist</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ All agent implementations completed</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Middleware components implemented</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Complete Agent Manager with orchestration</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Settings management system</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Health monitoring and error resolution</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>✅ Continuous learning system</span>
            </div>
          </div>
        </div>

        <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">⚠️ Integration Steps</h3>
          <ol className="text-sm space-y-2 list-decimal list-inside">
            <li>Copy all agent files to <code>components/agents/</code></li>
            <li>Copy middleware files to <code>components/middleware/</code></li>
            <li>Copy settings files to <code>components/settings/</code></li>
            <li>Update <code>components/agents/index.ts</code> with all exports</li>
            <li>Replace existing AgentManager with CompleteAgentManager</li>
            <li>Update main application to use CompleteAgentSystem component</li>
            <li>Configure API keys in settings</li>
            <li>Test system functionality with sample tasks</li>
          </ol>
        </div>

        <div className="bg-green-50 dark:bg-green-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">🎯 Usage Example</h3>
          <pre className="text-sm bg-background p-3 rounded border mt-2 overflow-auto">
{`// In your main application component
import { CompleteAgentSystem } from '@/components/agents/complete-integration';

export default function App() {
  return (
    <div className="h-screen">
      <CompleteAgentSystem />
    </div>
  );
}

// To submit tasks programmatically
const agentManager = new CompleteAgentManager();
const taskId = await agentManager.submitTask(
  "Create a React component for user authentication",
  ["auth.tsx", "types.ts"],
  "high"
);`}
          </pre>
        </div>

        <div className="bg-purple-50 dark:bg-purple-950 p-4 rounded-md">
          <h3 className="font-semibold mb-2">🔧 Key Features Available</h3>
          <ul className="text-sm space-y-1 list-disc list-inside">
            <li>🤖 9 specialized AI agents (Intern → Senior + Specialized)</li>
            <li>🧠 Intelligent task classification and routing</li>
            <li>📊 Real-time health monitoring and performance metrics</li>
            <li>🔄 Automatic error resolution with escalation</li>
            <li>📈 Continuous learning and optimization</li>
            <li>⚙️ Comprehensive settings management</li>
            <li>💰 Cost tracking and resource optimization</li>
            <li>🔒 Privacy-first architecture with local processing</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

// Main export
export default CompleteAgentSystem;