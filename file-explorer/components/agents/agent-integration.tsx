// components/agents/agent-integration.tsx
"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { AgentManager, AgentStatus, TaskAssignment } from './agent-manager';
import { AgentBase, AgentContext, AgentResponse, AgentMessage } from './agent-base';
import { useSharedAgentState } from './shared-agent-state';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Brain,
  Code,
  Search,
  Layers,
  Palette,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AgentIntegrationProps {
  className?: string;
}

export function AgentIntegration({ className }: AgentIntegrationProps) {
  const sharedState = useSharedAgentState();
  const [agentManager] = useState(() => new AgentManager());
  const [taskInput, setTaskInput] = useState('');
  const [isRunning, setIsRunning] = useState(false);

  // Use shared state instead of local state
  const agentStatuses = sharedState.agents;
  const activeTasks = sharedState.getActiveTasks();
  const messages = sharedState.messages;

  // Initialize agent manager and set up listeners
  useEffect(() => {
    // Set up message listener
    const handleMessage = (message: AgentMessage) => {
      // Add message to shared state instead of local state
      sharedState.addMessage({
        agentId: message.agentId,
        message: message.message,
        timestamp: message.timestamp,
        type: message.type || 'info'
      });
    };

    agentManager.onMessage(handleMessage);

    return () => {
      agentManager.offMessage(handleMessage);
    };
  }, [agentManager, sharedState]);

  const handleTaskSubmit = async () => {
    if (!taskInput.trim()) return;

    setIsRunning(true);
    try {
      // Add task to shared state
      await sharedState.assignTask({
        agentId: sharedState.selectedAgent,
        description: taskInput.trim(),
        status: 'pending',
        priority: 'medium'
      });

      const context: AgentContext = {
        task: taskInput.trim(),
        metadata: {
          requestedAt: Date.now(),
          source: 'ui'
        }
      };

      const taskId = await agentManager.assignTask(sharedState.selectedAgent, context, 'medium');
      console.log(`Task assigned with ID: ${taskId}`);

      // Clear input after successful submission
      setTaskInput('');
    } catch (error) {
      console.error('Failed to assign task:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getAgentIcon = (agentType: string) => {
    switch (agentType) {
      case 'orchestrator': return <Brain className="h-4 w-4" />;
      case 'implementation': return <Code className="h-4 w-4" />;
      case 'specialized': return <Zap className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getAgentColor = (agentId: string) => {
    const colors: Record<string, string> = {
      micromanager: 'bg-purple-500',
      intern: 'bg-green-500',
      junior: 'bg-blue-500',
      midlevel: 'bg-yellow-500',
      senior: 'bg-red-500',
      researcher: 'bg-indigo-500',
      architect: 'bg-gray-500',
      designer: 'bg-pink-500'
    };
    return colors[agentId] || 'bg-gray-400';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'idle': return 'text-green-500';
      case 'busy': return 'text-blue-500';
      case 'error': return 'text-red-500';
      case 'offline': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'text-green-500';
    if (score >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className={cn("h-full bg-background", className)}>
      <div className="flex flex-col h-full">
        <div className="border-b border-border p-4">
          <h2 className="text-xl font-semibold mb-4">AI Agent Orchestrator</h2>

          {/* Task Input */}
          <div className="space-y-3">
            <div className="flex gap-2">
              <select
                value={sharedState.selectedAgent}
                onChange={(e) => sharedState.setSelectedAgent(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background text-sm"
              >
                <option value="micromanager">🤖 Micromanager</option>
                <option value="intern">1️⃣ Intern</option>
                <option value="junior">2️⃣ Junior</option>
                <option value="midlevel">3️⃣ MidLevel</option>
                <option value="senior">4️⃣ Senior</option>
                <option value="researcher">📘 Researcher</option>
                <option value="architect">🏗️ Architect</option>
                <option value="designer">🎨 Designer</option>
              </select>
              <Button onClick={handleTaskSubmit} disabled={isRunning || !taskInput.trim()}>
                {isRunning ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
                {isRunning ? 'Processing...' : 'Execute'}
              </Button>
            </div>
            <Textarea
              placeholder="Describe the task you want the AI agent to perform..."
              value={taskInput}
              onChange={(e) => setTaskInput(e.target.value)}
              rows={3}
              className="resize-none"
            />
          </div>
        </div>

        <div className="flex-1 overflow-hidden">
          <Tabs defaultValue="agents" className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
              <TabsTrigger value="agents">Agents</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="messages">Messages</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TabsList>

            <TabsContent value="agents" className="flex-1 p-4 overflow-auto">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {agentStatuses.map((agent) => (
                  <Card key={agent.id} className="relative">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={cn("w-2 h-2 rounded-full", getAgentColor(agent.id))} />
                          <CardTitle className="text-sm">{agent.name}</CardTitle>
                        </div>
                        {getAgentIcon(agent.type)}
                      </div>
                      <CardDescription className="text-xs">
                        {agent.type} • {agent.status}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between text-xs">
                        <span>Health Score</span>
                        <span className={getHealthColor(agent.healthScore)}>
                          {agent.healthScore}%
                        </span>
                      </div>
                      <Progress value={agent.healthScore} className="h-1" />

                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-muted-foreground">Tasks:</span>
                          <span className="ml-1 font-medium">{agent.tasksCompleted}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Errors:</span>
                          <span className="ml-1 font-medium">{agent.errorCount}</span>
                        </div>
                      </div>

                      <div className="text-xs">
                        <span className="text-muted-foreground">Tokens:</span>
                        <span className="ml-1 font-medium">{agent.tokensUsed.toLocaleString()}</span>
                      </div>

                      {agent.currentTask && (
                        <div className="text-xs">
                          <span className="text-muted-foreground">Current:</span>
                          <div className="truncate mt-1 p-1 bg-muted rounded text-xs">
                            {agent.currentTask}
                          </div>
                        </div>
                      )}

                      <Badge
                        variant={agent.status === 'idle' ? 'default' : agent.status === 'busy' ? 'secondary' : 'destructive'}
                        className="text-xs"
                      >
                        <span className={getStatusColor(agent.status)}>●</span>
                        <span className="ml-1">{agent.status}</span>
                      </Badge>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="tasks" className="flex-1 p-4 overflow-auto">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Active Tasks</h3>
                  <Badge variant="outline">{activeTasks.length} active</Badge>
                </div>

                {activeTasks.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No active tasks</p>
                    <p className="text-sm">Submit a task above to get started</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {activeTasks.map((task) => (
                      <Card key={task.taskId}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {task.agentId}
                                </Badge>
                                <Badge
                                  variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {task.priority}
                                </Badge>
                              </div>
                              <p className="text-sm font-medium">{task.context.task}</p>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {formatTimestamp(task.createdAt)}
                            </div>
                          </div>

                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>Status: {task.status}</span>
                            {task.retryCount > 0 && (
                              <span>Retries: {task.retryCount}/{task.maxRetries}</span>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="messages" className="flex-1 p-4 overflow-auto">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Agent Messages</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => sharedState.clearMessages()}
                  >
                    Clear
                  </Button>
                </div>

                <ScrollArea className="h-96">
                  {messages.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No messages yet</p>
                      <p className="text-sm">Agent communications will appear here</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {messages.map((message, index) => (
                        <div
                          key={index}
                          className="p-3 rounded-md border bg-card text-card-foreground"
                        >
                          <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {message.agentId}
                              </Badge>
                              <Badge
                                variant={
                                  message.type === 'error' ? 'destructive' :
                                  message.type === 'completion' ? 'default' :
                                  'secondary'
                                }
                                className="text-xs"
                              >
                                {message.type}
                              </Badge>
                              {message.severity && (
                                <Badge
                                  variant={message.severity === 'high' ? 'destructive' : 'outline'}
                                  className="text-xs"
                                >
                                  {message.severity}
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {formatTimestamp(message.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm">{message.message}</p>
                          {message.actions && message.actions.length > 0 && (
                            <div className="mt-2">
                              <span className="text-xs text-muted-foreground">Actions: </span>
                              <span className="text-xs">{message.actions.join(', ')}</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="monitoring" className="flex-1 p-4 overflow-auto">
              <div className="grid gap-6 md:grid-cols-2">
                {/* System Health */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">System Health</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Overall Health</span>
                        <span className={getHealthColor(85)}>85%</span>
                      </div>
                      <Progress value={85} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Active Agents</span>
                        <div className="font-medium">
                          {agentStatuses.filter(a => a.status !== 'offline').length}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Total Tasks</span>
                        <div className="font-medium">
                          {agentStatuses.reduce((sum, a) => sum + a.tasksCompleted, 0)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Success Rate</span>
                        <span className="text-green-500">94%</span>
                      </div>
                      <Progress value={94} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Avg Response</span>
                        <div className="font-medium">2.3s</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Total Tokens</span>
                        <div className="font-medium">
                          {agentStatuses.reduce((sum, a) => sum + a.tokensUsed, 0).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}