"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { agentIP<PERSON><PERSON>ridge, AgentSystemState, AgentStatus, AgentMessage, AgentTask } from '@/lib/agent-ipc-bridge';

interface SharedAgentStateContextType {
  // State
  agents: AgentStatus[];
  messages: AgentMessage[];
  tasks: AgentTask[];
  isRunning: boolean;
  selectedAgent: string;
  
  // Actions
  updateAgentStatus: (agentId: string, status: AgentStatus['status'], healthScore?: number, tokensUsed?: number) => Promise<void>;
  addMessage: (message: Omit<AgentMessage, 'id'>) => Promise<void>;
  assignTask: (task: Omit<AgentTask, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateTask: (taskId: string, updates: Partial<AgentTask>) => Promise<void>;
  clearMessages: () => Promise<void>;
  setRunningState: (isRunning: boolean) => Promise<void>;
  setSelectedAgent: (agentId: string) => void;
  
  // Computed
  getAgentById: (id: string) => AgentStatus | undefined;
  getActiveTasks: () => AgentTask[];
  getTasksForAgent: (agentId: string) => AgentTask[];
}

const SharedAgentStateContext = createContext<SharedAgentStateContextType | null>(null);

export function useSharedAgentState() {
  const context = useContext(SharedAgentStateContext);
  if (!context) {
    throw new Error('useSharedAgentState must be used within a SharedAgentStateProvider');
  }
  return context;
}

interface SharedAgentStateProviderProps {
  children: React.ReactNode;
}

export function SharedAgentStateProvider({ children }: SharedAgentStateProviderProps) {
  const [state, setState] = useState<AgentSystemState>({
    agents: [],
    messages: [],
    tasks: [],
    isRunning: false,
    selectedAgent: 'micromanager'
  });

  // Initialize state from main process
  useEffect(() => {
    const initializeState = async () => {
      try {
        const initialState = await agentIPCBridge.getState();
        setState(initialState);
      } catch (error) {
        console.error('Failed to initialize agent state:', error);
      }
    };

    initializeState();

    // Set up event listeners for state updates
    const unsubscribe = agentIPCBridge.registerEventListeners({
      onStateUpdate: (newState) => {
        setState(newState);
      }
    });

    return unsubscribe;
  }, []);

  // Actions
  const updateAgentStatus = useCallback(async (
    agentId: string, 
    status: AgentStatus['status'], 
    healthScore?: number, 
    tokensUsed?: number
  ) => {
    try {
      await agentIPCBridge.updateAgentStatus(agentId, status, healthScore, tokensUsed);
    } catch (error) {
      console.error('Failed to update agent status:', error);
    }
  }, []);

  const addMessage = useCallback(async (message: Omit<AgentMessage, 'id'>) => {
    try {
      await agentIPCBridge.addMessage(message);
    } catch (error) {
      console.error('Failed to add message:', error);
    }
  }, []);

  const assignTask = useCallback(async (task: Omit<AgentTask, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      await agentIPCBridge.assignTask(task);
    } catch (error) {
      console.error('Failed to assign task:', error);
    }
  }, []);

  const updateTask = useCallback(async (taskId: string, updates: Partial<AgentTask>) => {
    try {
      await agentIPCBridge.updateTask(taskId, updates);
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  }, []);

  const clearMessages = useCallback(async () => {
    try {
      await agentIPCBridge.clearMessages();
    } catch (error) {
      console.error('Failed to clear messages:', error);
    }
  }, []);

  const setRunningState = useCallback(async (isRunning: boolean) => {
    try {
      await agentIPCBridge.setRunningState(isRunning);
    } catch (error) {
      console.error('Failed to set running state:', error);
    }
  }, []);

  const setSelectedAgent = useCallback((agentId: string) => {
    setState(prev => ({ ...prev, selectedAgent: agentId }));
  }, []);

  // Computed values
  const getAgentById = useCallback((id: string) => {
    return state.agents.find(agent => agent.id === id);
  }, [state.agents]);

  const getActiveTasks = useCallback(() => {
    return state.tasks.filter(task => task.status === 'running' || task.status === 'pending');
  }, [state.tasks]);

  const getTasksForAgent = useCallback((agentId: string) => {
    return state.tasks.filter(task => task.agentId === agentId);
  }, [state.tasks]);

  const contextValue: SharedAgentStateContextType = {
    // State
    agents: state.agents,
    messages: state.messages,
    tasks: state.tasks,
    isRunning: state.isRunning,
    selectedAgent: state.selectedAgent,
    
    // Actions
    updateAgentStatus,
    addMessage,
    assignTask,
    updateTask,
    clearMessages,
    setRunningState,
    setSelectedAgent,
    
    // Computed
    getAgentById,
    getActiveTasks,
    getTasksForAgent
  };

  return (
    <SharedAgentStateContext.Provider value={contextValue}>
      {children}
    </SharedAgentStateContext.Provider>
  );
}
