// components/agents/agent-base.ts
export interface AgentConfig {
  id: string;
  name: string;
  type: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

export interface AgentContext {
  task: string;
  files?: string[];
  codeContext?: string;
  rules?: string[];
  dependencies?: string[];
  metadata?: Record<string, any> & { kanbanCardId?: string; originalTaskId?: string; }; // Added kanbanCardId and originalTaskId
}

// Moved from agent-manager-complete.ts
export interface AgentStatus {
  id: string;
  name: string;
  type: 'orchestrator' | 'implementation' | 'specialized' | 'middleware';
  status: 'idle' | 'busy' | 'error' | 'offline';
  currentTask?: string;
  lastActivity: number;
  tokensUsed: number;
  tasksCompleted: number;
  errorCount: number;
  healthScore: number; // 0-100
  capabilities: string[];
}

// Moved from agent-manager-complete.ts
export interface TaskAssignment {
  taskId: string;
  agentId: string;
  context: AgentContext;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: number;
  retryCount: number;
  maxRetries: number;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed' | 'escalated';
  createdAt: number;
  updatedAt: number;
  escalationReason?: string;
  kanbanCardId?: string; // Add link to Kanban card
}

export interface AgentResponse {
  success: boolean;
  content?: string;
  error?: string;
  tokensUsed?: number;
  executionTime?: number;
  suggestions?: string[];
  metadata?: Record<string, any>;
}

export interface AgentMessage {
  agentId: string;
  taskId: string;
  type: 'update' | 'question' | 'completion' | 'error';
  message: string;
  severity?: 'low' | 'medium' | 'high';
  actions?: string[];
  timestamp: number;
}

export abstract class AgentBase {
  protected config: AgentConfig;
  protected context: AgentContext | null = null;
  protected conversationHistory: AgentMessage[] = [];
  
  constructor(config: AgentConfig) {
    this.config = config;
  }

  // Abstract methods that must be implemented by subclasses
  abstract execute(context: AgentContext): Promise<AgentResponse>;
  abstract getCapabilities(): string[];
  abstract getSystemPrompt(): string;

  // Common functionality for all agents
  public getId(): string {
    return this.config.id;
  }

  public getName(): string {
    return this.config.name;
  }

  public getType(): string {
    return this.config.type;
  }

  public setContext(context: AgentContext): void {
    this.context = context;
  }

  public getContext(): AgentContext | null {
    return this.context;
  }

  public addMessage(message: Omit<AgentMessage, 'agentId' | 'timestamp'>): void {
    const agentMessage: AgentMessage = {
      ...message,
      agentId: this.config.id,
      timestamp: Date.now()
    };
    this.conversationHistory.push(agentMessage);
  }

  public getConversationHistory(): AgentMessage[] {
    return [...this.conversationHistory];
  }

  public clearHistory(): void {
    this.conversationHistory = [];
  }

  // Utility method for creating structured communication messages
  protected createMessage(
    taskId: string,
    type: AgentMessage['type'],
    message: string,
    severity?: AgentMessage['severity'],
    actions?: string[]
  ): AgentMessage {
    return {
      agentId: this.config.id,
      taskId,
      type,
      message,
      severity,
      actions,
      timestamp: Date.now()
    };
  }

  // Method to validate context before execution
  protected validateContext(context: AgentContext): { valid: boolean; error?: string } {
    if (!context.task || context.task.trim().length === 0) {
      return { valid: false, error: 'Task description is required' };
    }
    return { valid: true };
  }

  // Method to estimate token usage (to be overridden by specific agents)
  protected estimateTokens(context: AgentContext): number {
    const baseTokens = context.task.length / 4; // Rough estimate: 4 chars per token
    const contextTokens = context.codeContext ? context.codeContext.length / 4 : 0;
    const rulesTokens = context.rules ? context.rules.join(' ').length / 4 : 0;
    return Math.ceil(baseTokens + contextTokens + rulesTokens);
  }

  // Method to create error response
  protected createErrorResponse(error: string, tokensUsed = 0): AgentResponse {
    return {
      success: false,
      error,
      tokensUsed,
      executionTime: 0
    };
  }

  // Method to create success response
  protected createSuccessResponse(
    content: string,
    tokensUsed = 0,
    executionTime = 0,
    suggestions?: string[],
    metadata?: Record<string, any>
  ): AgentResponse {
    return {
      success: true,
      content,
      tokensUsed,
      executionTime,
      suggestions,
      metadata
    };
  }
}