"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { CardType } from "./board-context"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  DialogTit<PERSON>,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface CreateCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateCard: (card: any) => void
  cardTypes: CardType[]
}

export function CreateCardDialog({
  open,
  onOpenChange,
  onCreateCard,
  cardTypes,
}: CreateCardDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [priority, setPriority] = useState<string>("")
  const [projectId, setProjectId] = useState("")
  const [tags, setTags] = useState("")

  useEffect(() => {
    if (open && cardTypes.length > 0) {
      setPriority(cardTypes[0].id);
    } else if (open) {
      setPriority(""); // Fallback if no card types
    }
  }, [open, cardTypes]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const newCard = {
      title,
      description,
      priority,
      projectId,
      tags: tags.split(",").map(tag => tag.trim()).filter(Boolean),
      progress: 0,
      labels: [],
      agentAssignments: [],
      dependencies: [],
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0,
      },
      taskHistory: [],
    }
    
    onCreateCard(newCard)
    resetForm()
    onOpenChange(false)
  }
  
  const resetForm = () => {
    setTitle("")
    setDescription("")
    if (cardTypes.length > 0) {
      setPriority(cardTypes[0].id);
    } else {
      setPriority("");
    }
    setProjectId("")
    setTags("")
  }

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen) resetForm();
      onOpenChange(isOpen);
    }}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Card</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="title" className="text-right text-sm">
                Title
              </label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="description" className="text-right text-sm">
                Description
              </label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="priority" className="text-right text-sm">
                Priority
              </label>
              <Select
                value={priority}
                onValueChange={(value) => setPriority(value)}
                disabled={cardTypes.length === 0}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {cardTypes.length > 0 ? (
                    cardTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>No priorities configured</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="projectId" className="text-right text-sm">
                Project ID
              </label>
              <Input
                id="projectId"
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                className="col-span-3"
                placeholder="e.g. TASK-123"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="tags" className="text-right text-sm">
                Tags
              </label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="col-span-3"
                placeholder="Comma-separated tags"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => { resetForm(); onOpenChange(false); }}>
              Cancel
            </Button>
            <Button type="submit">Create Card</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}