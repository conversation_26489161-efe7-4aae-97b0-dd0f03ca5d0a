"use strict";
/**
 * Board IPC Bridge
 *
 * This service handles communication between floating and fixed Kanban views
 * via Electron IPC to maintain synchronized state across all windows.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoardAgentAPI = exports.boardIPCBridge = exports.BoardIPCBridge = exports.BOARD_COMMANDS = exports.BOARD_EVENTS = void 0;
// Board events coming from main process
exports.BOARD_EVENTS = {
    STATE_UPDATE: 'board:state-update',
    CARD_CREATED: 'board:card-created',
    CARD_UPDATED: 'board:card-updated',
    CARD_DELETED: 'board:card-deleted',
    CARD_MOVED: 'board:card-moved',
    COLUMN_UPDATED: 'board:column-updated',
    AGENT_STATUS: 'board:agent-status',
    AGENT_LOG: 'board:agent-log',
};
// Board commands sent to main process
exports.BOARD_COMMANDS = {
    GET_STATE: 'board:get-state',
    CREATE_CARD: 'board:create-card',
    UPDATE_CARD: 'board:update-card',
    DELETE_CARD: 'board:delete-card',
    MOVE_CARD: 'board:move-card',
    UPDATE_COLUMN: 'board:update-column',
    UPDATE_BOARD: 'board:update-board',
};
class BoardIPCBridge {
    constructor() {
        this.isElectron = typeof window !== 'undefined' && window.electronAPI;
    }
    // Register listeners for events from main process
    registerEventListeners(handlers) {
        if (!this.isElectron)
            return () => { };
        const { ipcRenderer } = require('electron');
        if (handlers.onStateUpdate) {
            ipcRenderer.on(exports.BOARD_EVENTS.STATE_UPDATE, (_, state) => handlers.onStateUpdate(state));
        }
        if (handlers.onCardCreated) {
            ipcRenderer.on(exports.BOARD_EVENTS.CARD_CREATED, (_, card) => handlers.onCardCreated(card));
        }
        if (handlers.onCardUpdated) {
            ipcRenderer.on(exports.BOARD_EVENTS.CARD_UPDATED, (_, card) => handlers.onCardUpdated(card));
        }
        if (handlers.onCardDeleted) {
            ipcRenderer.on(exports.BOARD_EVENTS.CARD_DELETED, (_, cardId, columnId) => handlers.onCardDeleted(cardId, columnId));
        }
        if (handlers.onCardMoved) {
            ipcRenderer.on(exports.BOARD_EVENTS.CARD_MOVED, (_, cardId, sourceColumnId, targetColumnId, swimlaneId) => handlers.onCardMoved(cardId, sourceColumnId, targetColumnId, swimlaneId));
        }
        if (handlers.onColumnUpdated) {
            ipcRenderer.on(exports.BOARD_EVENTS.COLUMN_UPDATED, (_, column) => handlers.onColumnUpdated(column));
        }
        if (handlers.onAgentStatus) {
            ipcRenderer.on(exports.BOARD_EVENTS.AGENT_STATUS, (_, status) => handlers.onAgentStatus(status));
        }
        if (handlers.onAgentLog) {
            ipcRenderer.on(exports.BOARD_EVENTS.AGENT_LOG, (_, log) => handlers.onAgentLog(log));
        }
        // Return unsubscribe function
        return () => {
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.STATE_UPDATE);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.CARD_CREATED);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.CARD_UPDATED);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.CARD_DELETED);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.CARD_MOVED);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.COLUMN_UPDATED);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.AGENT_STATUS);
            ipcRenderer.removeAllListeners(exports.BOARD_EVENTS.AGENT_LOG);
        };
    }
    // Send commands to main process
    async getBoardState(boardId) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.GET_STATE, boardId);
    }
    async createCard(boardId, columnId, cardData) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.CREATE_CARD, boardId, columnId, cardData);
    }
    async updateCard(boardId, cardId, updates) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.UPDATE_CARD, boardId, cardId, updates);
    }
    async deleteCard(boardId, columnId, cardId) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.DELETE_CARD, boardId, columnId, cardId);
    }
    async moveCard(boardId, cardId, sourceColumnId, targetColumnId, swimlaneId, targetIndex) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.MOVE_CARD, boardId, cardId, sourceColumnId, targetColumnId, swimlaneId, targetIndex);
    }
    async updateColumn(boardId, columnId, updates) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.UPDATE_COLUMN, boardId, columnId, updates);
    }
    async updateBoard(boardId, updates) {
        if (!this.isElectron)
            return null;
        const { ipcRenderer } = require('electron');
        return ipcRenderer.invoke(exports.BOARD_COMMANDS.UPDATE_BOARD, boardId, updates);
    }
}
exports.BoardIPCBridge = BoardIPCBridge;
exports.boardIPCBridge = new BoardIPCBridge();
/**
 * Board Agent API
 *
 * This service provides methods for AI agents to interact with the Kanban board.
 */
class BoardAgentAPI {
    constructor(boardContext) {
        this.boardContext = boardContext;
    }
    /**
     * Get the current state of the board
     */
    getBoardState() {
        return {
            columns: this.boardContext.columns,
            cards: this.getAllCards(),
            swimlanes: this.boardContext.swimlanes,
        };
    }
    /**
     * Create a new task card
     */
    createTaskCard(task, agentId) {
        const now = new Date().toISOString();
        const newCard = {
            id: `card-${Date.now()}`,
            title: task.title,
            description: task.description,
            priority: task.priority || "medium",
            projectId: task.projectId || `TASK-${Math.floor(Math.random() * 1000)}`,
            dueDate: task.dueDate,
            assignee: task.assignee,
            tags: task.tags || [],
            subtasks: task.subtasks || [],
            swimlaneId: task.swimlaneId || "default",
            storyPoints: task.storyPoints,
            agentAssignments: [
                {
                    agentId,
                    agentType: "AI",
                    assignmentTime: now,
                },
            ],
            dependencies: task.dependencies || [],
            resourceMetrics: {
                tokenUsage: 0,
                cpuTime: 0,
                memoryUsage: 0,
            },
            taskHistory: [
                {
                    timestamp: now,
                    action: "created",
                    agentId,
                    details: "Card created by AI agent",
                },
            ],
            // Ensure columnId is present for addCard to work correctly in board-context
            columnId: task.columnId || (this.boardContext.columns && this.boardContext.columns.length > 0 ? this.boardContext.columns[0].id : "column-1"), // Default to first column if available
            progress: task.progress || 0, // Ensure progress is initialized
            labels: task.labels || [], // Ensure labels are initialized
            createdAt: now,
            updatedAt: now,
        };
        // Add the card to the first column (usually backlog)
        // The boardContext.addCard method expects boardId, columnId, and cardData (without id).
        // The adapter in agent-board-controller.tsx maps this.boardContext.addCard(newCard)
        // to boardContext.addCardToColumn(activeBoard.id, newCard.columnId, newCard).
        // So `newCard` must contain `columnId`.
        this.boardContext.addCard(newCard);
        // Try to sync with main process in background
        const cardData = { ...newCard };
        delete cardData.id; // Remove id for IPC call
        exports.boardIPCBridge.createCard('main', newCard.columnId, cardData).catch(error => {
            console.warn('Failed to sync agent card creation with main process:', error);
        });
        return newCard;
    }
    /**
     * Move a card to a different column
     */
    moveCardToColumn(cardId, columnId, agentId) {
        const card = this.getCardById(cardId);
        if (!card)
            return null;
        const sourceColumnId = card.columnId;
        const swimlaneId = card.swimlaneId || "default"; // Keep existing swimlane or default
        // Add history entry
        const updatedCard = {
            ...card,
            columnId,
            // Pass the swimlaneId when moving, as moveCard in board-context needs it
            swimlaneId,
            taskHistory: [
                ...card.taskHistory,
                {
                    timestamp: new Date().toISOString(),
                    action: "moved",
                    agentId,
                    details: `Moved from column ${sourceColumnId} to ${columnId}`,
                },
            ],
        };
        // Call moveCard directly if the context provides it, or updateCard if not.
        // The adapter in agent-board-controller.tsx maps boardContext.updateCard(updatedCard)
        // to boardContext.updateCardInColumn(activeBoard.id, updatedCard.columnId, updatedCard).
        // For a real move, it's better to call the moveCard method in boardContext directly.
        // However, the current adapter only maps updateCard. So, this will trigger an update in the UI.
        // Future improvement: expose `moveCard` directly in the adapter.
        // For now, rely on `updateCard` which should trigger the UI update correctly.
        // The `moveCard` function in `board-context` already handles removal from source and adding to destination.
        // Let's call the `moveCard` function of the context if it exists, otherwise fallback to `updateCard`.
        if (typeof this.boardContext.moveCard === 'function') {
            // This is a direct call for a real move, the `moveCard` in board-context needs specific args
            // This will require boardContext adapter to expose `moveCard` similarly to `addCard`.
            // Given current adapter, it relies on updateCard which is less efficient for moves but works.
            // For now, `updateCard` is sufficient for internal state change.
            this.boardContext.updateCard(updatedCard); // This will update the card object in its existing column
        }
        else {
            this.boardContext.updateCard(updatedCard);
        }
        return updatedCard;
    }
    /**
     * Update card progress
     */
    updateCardProgress(cardId, progress, agentId) {
        const card = this.getCardById(cardId);
        if (!card)
            return null;
        const updatedCard = {
            ...card,
            progress: Math.min(100, Math.max(0, progress)),
            taskHistory: [
                ...card.taskHistory,
                {
                    timestamp: new Date().toISOString(),
                    action: "progress-update",
                    agentId,
                    details: `Updated progress to ${progress}%`,
                },
            ],
        };
        this.boardContext.updateCard(updatedCard);
        return updatedCard;
    }
    /**
     * Update card resource metrics
     */
    updateCardResourceMetrics(cardId, metrics, agentId) {
        const card = this.getCardById(cardId);
        if (!card)
            return null;
        const updatedCard = {
            ...card,
            resourceMetrics: {
                ...card.resourceMetrics,
                ...metrics,
            },
            taskHistory: [
                ...card.taskHistory,
                {
                    timestamp: new Date().toISOString(),
                    action: "resource-update",
                    agentId,
                    details: `Updated resource metrics`,
                },
            ],
        };
        this.boardContext.updateCard(updatedCard);
        return updatedCard;
    }
    /**
     * Add a dependency between cards
     */
    addCardDependency(cardId, dependencyCardId, agentId) {
        const card = this.getCardById(cardId);
        if (!card)
            return null;
        // Don't add if already a dependency
        if (card.dependencies.includes(dependencyCardId)) {
            return card;
        }
        const updatedCard = {
            ...card,
            dependencies: [...card.dependencies, dependencyCardId],
            taskHistory: [
                ...card.taskHistory,
                {
                    timestamp: new Date().toISOString(),
                    action: "dependency-added",
                    agentId,
                    details: `Added dependency on card ${dependencyCardId}`,
                },
            ],
        };
        this.boardContext.updateCard(updatedCard);
        return updatedCard;
    }
    /**
     * Remove a dependency between cards
     */
    removeCardDependency(cardId, dependencyCardId, agentId) {
        const card = this.getCardById(cardId);
        if (!card)
            return null;
        const updatedCard = {
            ...card,
            dependencies: card.dependencies.filter((id) => id !== dependencyCardId),
            taskHistory: [
                ...card.taskHistory,
                {
                    timestamp: new Date().toISOString(),
                    action: "dependency-removed",
                    agentId,
                    details: `Removed dependency on card ${dependencyCardId}`,
                },
            ],
        };
        this.boardContext.updateCard(updatedCard);
        return updatedCard;
    }
    /**
     * Get all cards across all columns
     */
    getAllCards() {
        if (!this.boardContext.columns)
            return [];
        return this.boardContext.columns.flatMap((column) => column.cards);
    }
    /**
     * Get a card by ID
     */
    getCardById(cardId) {
        const allCards = this.getAllCards();
        return allCards.find((card) => card.id === cardId);
    }
}
exports.BoardAgentAPI = BoardAgentAPI;
