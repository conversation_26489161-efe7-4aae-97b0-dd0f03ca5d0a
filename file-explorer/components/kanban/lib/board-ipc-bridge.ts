// components/kanban/lib/board-ipc-bridge.ts
import { BOARD_COMMANDS, BOARD_EVENTS } from '../../../electron/board-constants'; // Adjust path
import type { BoardFull, Card, Column, Swimlane, CardType, Agent } from '../board-context'; // Adjust path

type EventHandlers = {
  onStateUpdate?: (boardId: string, state: BoardFull) => void;
  onBoardListUpdate?: (boards: {id: string, name: string, description?: string}[]) => void;
  // Add more specific event handlers if needed, e.g., onCardCreated, onCardMoved
};

export class BoardIPCBridge {
  private isElectron: boolean;
  private ipcRenderer: any; // To hold electron.ipcRenderer if available

  constructor() {
    this.isElectron = typeof window !== 'undefined' && (window as any).electronAPI;
    if (this.isElectron) {
      // ipc<PERSON><PERSON><PERSON> is not directly exposed by preload.js in the example.
      // preload.js exposes specific functions via electronAPI.
      // For general send/invoke/on, we'd need to expose them or use a workaround.
      // For now, assuming direct ipcRenderer access for simplicity if this component is used in an Electron context
      // This would typically be: this.ipcRenderer = require('electron').ipcRenderer;
      // However, with contextIsolation, this is not directly possible from renderer code.
      // The preload script must expose ipcRenderer.on, invoke, send.
      // Let's assume preload.js will be updated to expose these generally or specifically for board.
      // For now, we'll use a placeholder and log a warning if not in Electron.
      // A better approach for preload.js:
      // contextBridge.exposeInMainWorld('ipc', {
      //   send: (channel, data) => ipcRenderer.send(channel, data),
      //   invoke: (channel, data) => ipcRenderer.invoke(channel, data),
      //   on: (channel, func) => ipcRenderer.on(channel, (event, ...args) => func(...args)),
      //   removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
      // });
      // Then here: this.ipcRenderer = (window as any).ipc;
      // Using a simplified assumption that electronAPI might provide invoke/send/on:
       this.ipcRenderer = (window as any).electronAPI?.ipcRendererInternal // This is hypothetical
       if (!this.ipcRenderer && this.isElectron) {
           console.warn("BoardIPCBridge: ipcRenderer not available via electronAPI. Full IPC functionality might be limited. Ensure preload.js exposes invoke, send, and on methods for 'board:' channels or generically.");
           // As a fallback, we will attempt to use direct ipcRenderer if it's somehow available,
           // which is unlikely in a properly sandboxed renderer.
           // This part is more of a placeholder for correct preload setup.
           try {
            // This line will likely fail in a sandboxed renderer but is here for conceptual completeness
            // if the preload script was not set up to expose generic IPC methods.
            const electron = require('electron');
            this.ipcRenderer = electron.ipcRenderer;
           } catch (e) {
            console.error("Failed to require electron.ipcRenderer directly.", e);
           }
       }
    } else {
      console.warn("BoardIPCBridge: Not running in Electron. IPC functionality will be disabled.");
    }
  }

  private getIpcRenderer() {
    // This method standardizes access, assuming preload.js exposes a generic 'ipc' object
    // as suggested in the constructor comment.
    // if ((window as any).ipc) return (window as any).ipc;
    
    // Fallback to direct require if available (less secure, typically for non-sandboxed or main process code)
    // For renderer, this should come from preload.
    // Given the current preload.js, it doesn't expose generic on/invoke.
    // This highlights a need to update preload.js.
    // For now, we'll operate on the assumption that calls to invoke will be made available.
    // The listeners require `ipcRenderer.on`.
    if (this.isElectron && (window as any).electronAPI) {
        // The current preload.js doesn't expose `on` or `removeAllListeners`
        // This is a structural issue. We'll mock it for compilation for now.
        return {
            invoke: (window as any).electronAPI.invokeBoardCommand || ((channel: string, ...args: any[]) => (window as any).electronAPI[channel.replace(":", "_")]?.(...args) || Promise.reject("IPC method not exposed")),
            on: (window as any).electronAPI.onBoardEvent || ((channel: string, listener: Function) => console.warn(`IPC 'on' for ${channel} not exposed`)),
            removeAllListeners: (window as any).electronAPI.removeAllBoardListeners || ((channel: string) => console.warn(`IPC 'removeAllListeners' for ${channel} not exposed`)),
        };
    }
    return null;
  }


  registerEventListeners(handlers: EventHandlers): () => void {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return () => {};

    const stateUpdateListener = (_event: any, boardId: string, state: BoardFull) => {
      if (handlers.onStateUpdate) handlers.onStateUpdate(boardId, state);
    };
    const boardListListener = (_event: any, boards: {id: string, name: string, description?: string}[]) => {
      if (handlers.onBoardListUpdate) handlers.onBoardListUpdate(boards);
    };
    
    ipc.on(BOARD_EVENTS.STATE_UPDATE, stateUpdateListener);
    ipc.on(BOARD_EVENTS.BOARD_LIST_UPDATED, boardListListener);

    return () => {
      ipc.removeAllListeners(BOARD_EVENTS.STATE_UPDATE);
      ipc.removeAllListeners(BOARD_EVENTS.BOARD_LIST_UPDATED);
    };
  }

  async getBoardState(boardId: string): Promise<BoardFull | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) {
      console.warn("IPC not available for getBoardState");
      return null; // Or throw error, or return mock data for non-electron env
    }
    try {
      return await ipc.invoke(BOARD_COMMANDS.GET_STATE, boardId);
    } catch (error) {
      console.error(`Error invoking ${BOARD_COMMANDS.GET_STATE}:`, error);
      return null;
    }
  }

  async createBoard(name: string, description?: string): Promise<BoardFull | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.CREATE_BOARD, name, description);
  }
  
  async updateBoardMetadata(boardId: string, name: string, description?: string): Promise<BoardFull | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_BOARD_METADATA, boardId, name, description);
  }

  async deleteBoard(boardId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_BOARD, boardId);
  }
  
  async addColumn(boardId: string, title: string): Promise<Column | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.ADD_COLUMN, boardId, title);
  }

  async updateColumn(boardId: string, columnData: Column): Promise<Column | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_COLUMN, boardId, columnData);
  }
  
  async deleteColumn(boardId: string, columnId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_COLUMN, boardId, columnId);
  }
  
  async moveColumn(boardId: string, dragId: string, overId: string | null): Promise<Column[] | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.MOVE_COLUMN, boardId, dragId, overId);
  }

  async addSwimlane(boardId: string, title: string): Promise<Swimlane | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.ADD_SWIMLANE, boardId, title);
  }
  
  async updateSwimlane(boardId: string, swimlaneData: Swimlane): Promise<Swimlane | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_SWIMLANE, boardId, swimlaneData);
  }

  async deleteSwimlane(boardId: string, swimlaneId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_SWIMLANE, boardId, swimlaneId);
  }
  
  async toggleSwimlaneExpansion(boardId: string, swimlaneId: string): Promise<Swimlane | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.TOGGLE_SWIMLANE_EXPANSION, boardId, swimlaneId);
  }

  async createCard(boardId: string, columnId: string, cardData: Omit<Card, "id" | "createdAt" | "updatedAt">): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.CREATE_CARD, boardId, columnId, cardData);
  }

  async updateCard(boardId: string, cardData: Card): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_CARD, boardId, cardData);
  }

  async deleteCard(boardId: string, columnId: string, cardId: string): Promise<boolean | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.DELETE_CARD, boardId, columnId, cardId);
  }

  async moveCard(boardId: string, cardId: string, sourceColumnId: string, destinationColumnId: string, destinationSwimlaneId: string): Promise<Card | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.MOVE_CARD, boardId, cardId, sourceColumnId, destinationColumnId, destinationSwimlaneId);
  }
  
  async updateCardTypes(boardId: string, cardTypes: CardType[]): Promise<CardType[] | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_CARD_TYPES, boardId, cardTypes);
  }
  
  async updateAgentsOnBoard(boardId: string, agents: Agent[]): Promise<Agent[] | null> {
    const ipc = this.getIpcRenderer();
    if (!ipc || !this.isElectron) return null;
    return ipc.invoke(BOARD_COMMANDS.UPDATE_AGENTS_ON_BOARD, boardId, agents);
  }
}

export const boardIPCBridge = new BoardIPCBridge();