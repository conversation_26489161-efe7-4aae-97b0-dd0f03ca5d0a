// components/middleware/agent-state-monitor.ts
import { AgentBase, AgentConfig, AgentContext, AgentResponse, AgentMessage } from '../agents/agent-base';

export interface AgentHealthMetrics {
  agentId: string;
  tokensPerMinute: number;
  errorRate: number; // 0-1
  averageResponseTime: number; // milliseconds
  consistencyScore: number; // 0-1
  completionRate: number; // 0-1
  lastActiveTime: number;
  totalTasks: number;
  failedTasks: number;
}

export interface HealthAlert {
  agentId: string;
  severity: 'yellow' | 'red';
  message: string;
  timestamp: number;
  suggestedActions: string[];
}

export class AgentStateMonitorAgent extends AgentBase {
  private healthMetrics: Map<string, AgentHealthMetrics> = new Map();
  private healthHistory: Map<string, AgentHealthMetrics[]> = new Map();
  private alerts: HealthAlert[] = [];
  private alertListeners: ((alert: HealthAlert) => void)[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(config: AgentConfig) {
    super(config);
    this.startMonitoring();
  }

  public getCapabilities(): string[] {
    return [
      'agent_health_tracking',
      'performance_monitoring',
      'intervention_protocols',
      'model_evaluation',
      'alert_generation'
    ];
  }

  public getSystemPrompt(): string {
    return `You are the Agent State Monitor, the critical reliability layer that tracks agent performance and ensures system health.

CORE RESPONSIBILITIES:
1. Monitor health and performance of all agents in real-time
2. Calculate composite health scores for each agent
3. Implement intervention protocols for degraded performance
4. Generate alerts for critical issues
5. Track performance metrics and trends
6. Evaluate model suitability for different agent types

INTERVENTION PROTOCOLS:
- YELLOW status: Flag agent, enhance prompts, allow self-correction
- RED status: Issue alerts, pause operations, substitute models
- System-wide issues: Emergency notifications, safe mode activation

Always prioritize early detection and prevention over reactive intervention.`;
  }

  public async execute(context: AgentContext): Promise<AgentResponse> {
    const startTime = Date.now();

    try {
      const validation = this.validateContext(context);
      if (!validation.valid) {
        return this.createErrorResponse(validation.error!);
      }

      const analysis = await this.analyzeSystemHealth();
      const report = this.generateHealthReport(analysis);

      const executionTime = Date.now() - startTime;
      const tokensUsed = this.estimateTokens(context) + 300;

      return this.createSuccessResponse(
        report,
        tokensUsed,
        executionTime,
        this.generateHealthSuggestions(analysis),
        { 
          systemHealth: analysis.overallHealth,
          criticalAgents: analysis.criticalAgents,
          alertCount: this.alerts.length
        }
      );

    } catch (error) {
      const executionTime = Date.now() - startTime;
      return this.createErrorResponse(
        `Health monitoring failed: ${error instanceof Error ? error.message : String(error)}`,
        this.estimateTokens(context)
      );
    }
  }

  // Public methods for agent registration and updates
  public registerAgent(agentId: string): void {
    if (!this.healthMetrics.has(agentId)) {
      this.healthMetrics.set(agentId, {
        agentId,
        tokensPerMinute: 0,
        errorRate: 0,
        averageResponseTime: 0,
        consistencyScore: 1,
        completionRate: 1,
        lastActiveTime: Date.now(),
        totalTasks: 0,
        failedTasks: 0
      });
      this.healthHistory.set(agentId, []);
    }
  }

  public updateAgentMetrics(agentId: string, update: {
    tokensUsed?: number;
    responseTime?: number;
    success?: boolean;
    taskCompleted?: boolean;
  }): void {
    const metrics = this.healthMetrics.get(agentId);
    if (!metrics) {
      this.registerAgent(agentId);
      return this.updateAgentMetrics(agentId, update);
    }

    const now = Date.now();
    const timeDiff = (now - metrics.lastActiveTime) / 1000 / 60; // minutes

    // Update metrics
    if (update.tokensUsed) {
      // Calculate tokens per minute with exponential smoothing
      const newRate = timeDiff > 0 ? update.tokensUsed / timeDiff : 0;
      metrics.tokensPerMinute = metrics.tokensPerMinute * 0.7 + newRate * 0.3;
    }

    if (update.responseTime) {
      // Update average response time with exponential smoothing
      metrics.averageResponseTime = metrics.averageResponseTime * 0.8 + update.responseTime * 0.2;
    }

    if (update.taskCompleted !== undefined) {
      metrics.totalTasks++;
      if (!update.success) {
        metrics.failedTasks++;
      }
      metrics.completionRate = (metrics.totalTasks - metrics.failedTasks) / metrics.totalTasks;
      metrics.errorRate = metrics.failedTasks / metrics.totalTasks;
    }

    metrics.lastActiveTime = now;

    // Calculate overall health score
    const healthScore = this.calculateHealthScore(metrics);
    
    // Check for intervention needs
    this.checkInterventionNeeds(agentId, metrics, healthScore);

    // Store historical data (keep last 100 entries)
    const history = this.healthHistory.get(agentId)!;
    history.push({ ...metrics });
    if (history.length > 100) {
      history.shift();
    }
  }

  public getAgentHealth(agentId: string): AgentHealthMetrics | null {
    return this.healthMetrics.get(agentId) || null;
  }

  public getAllAgentHealth(): AgentHealthMetrics[] {
    return Array.from(this.healthMetrics.values());
  }

  public getAlerts(): HealthAlert[] {
    return [...this.alerts];
  }

  public clearAlert(agentId: string): void {
    this.alerts = this.alerts.filter(alert => alert.agentId !== agentId);
  }

  public onAlert(listener: (alert: HealthAlert) => void): void {
    this.alertListeners.push(listener);
  }

  public offAlert(listener: (alert: HealthAlert) => void): void {
    const index = this.alertListeners.indexOf(listener);
    if (index > -1) {
      this.alertListeners.splice(index, 1);
    }
  }

  private startMonitoring(): void {
    // Run health checks every 5 seconds
    this.monitoringInterval = setInterval(() => {
      this.performHealthChecks();
    }, 5000);
  }

  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  private performHealthChecks(): void {
    const now = Date.now();
    
    for (const [agentId, metrics] of this.healthMetrics) {
      // Check for inactive agents
      const inactiveTime = now - metrics.lastActiveTime;
      if (inactiveTime > 300000) { // 5 minutes
        this.generateAlert(agentId, 'yellow', 'Agent has been inactive for more than 5 minutes', [
          'Check agent status',
          'Restart agent if necessary'
        ]);
      }

      // Check error rates
      if (metrics.errorRate > 0.3) {
        this.generateAlert(agentId, 'red', `High error rate: ${(metrics.errorRate * 100).toFixed(1)}%`, [
          'Review recent errors',
          'Consider model adjustment',
          'Check prompt effectiveness'
        ]);
      } else if (metrics.errorRate > 0.15) {
        this.generateAlert(agentId, 'yellow', `Elevated error rate: ${(metrics.errorRate * 100).toFixed(1)}%`, [
          'Monitor error patterns',
          'Consider prompt enhancement'
        ]);
      }

      // Check response times
      if (metrics.averageResponseTime > 30000) { // 30 seconds
        this.generateAlert(agentId, 'red', 'Very slow response times detected', [
          'Check system resources',
          'Consider model optimization',
          'Review task complexity'
        ]);
      } else if (metrics.averageResponseTime > 15000) { // 15 seconds
        this.generateAlert(agentId, 'yellow', 'Slow response times detected', [
          'Monitor performance trends',
          'Consider optimization'
        ]);
      }
    }
  }

  private calculateHealthScore(metrics: AgentHealthMetrics): number {
    // Weighted health score calculation
    const errorWeight = 0.3;
    const responseTimeWeight = 0.2;
    const completionWeight = 0.3;
    const consistencyWeight = 0.2;

    // Normalize error rate (lower is better)
    const errorScore = Math.max(0, 1 - metrics.errorRate * 2);

    // Normalize response time (assuming 5 seconds is good, 30+ seconds is bad)
    const responseScore = Math.max(0, Math.min(1, (30000 - metrics.averageResponseTime) / 25000));

    // Completion rate is already 0-1
    const completionScore = metrics.completionRate;

    // Consistency score is already 0-1
    const consistencyScore = metrics.consistencyScore;

    const healthScore = (
      errorScore * errorWeight +
      responseScore * responseTimeWeight +
      completionScore * completionWeight +
      consistencyScore * consistencyWeight
    ) * 100;

    return Math.max(0, Math.min(100, healthScore));
  }

  private checkInterventionNeeds(agentId: string, metrics: AgentHealthMetrics, healthScore: number): void {
    // Clear existing alerts for this agent if health improved
    if (healthScore > 70) {
      this.clearAlert(agentId);
      return;
    }

    // Generate new alerts based on health score
    if (healthScore < 40) {
      this.generateAlert(agentId, 'red', `Critical health score: ${healthScore.toFixed(1)}%`, [
        'Immediate intervention required',
        'Consider agent restart',
        'Review configuration',
        'Switch to backup model'
      ]);
    } else if (healthScore < 60) {
      this.generateAlert(agentId, 'yellow', `Low health score: ${healthScore.toFixed(1)}%`, [
        'Monitor closely',
        'Consider prompt optimization',
        'Review recent performance'
      ]);
    }
  }

  private generateAlert(agentId: string, severity: 'yellow' | 'red', message: string, actions: string[]): void {
    // Don't duplicate recent alerts
    const recentAlert = this.alerts.find(a => 
      a.agentId === agentId && 
      a.severity === severity && 
      a.message === message &&
      Date.now() - a.timestamp < 60000 // Within last minute
    );

    if (recentAlert) return;

    const alert: HealthAlert = {
      agentId,
      severity,
      message,
      timestamp: Date.now(),
      suggestedActions: actions
    };

    this.alerts.push(alert);

    // Keep only last 50 alerts
    if (this.alerts.length > 50) {
      this.alerts.shift();
    }

    // Notify listeners
    this.alertListeners.forEach(listener => listener(alert));
  }

  private async analyzeSystemHealth(): Promise<{
    overallHealth: number;
    criticalAgents: string[];
    healthyAgents: string[];
    systemStatus: 'healthy' | 'degraded' | 'critical';
    recommendations: string[];
  }> {
    const allMetrics = Array.from(this.healthMetrics.values());
    
    if (allMetrics.length === 0) {
      return {
        overallHealth: 0,
        criticalAgents: [],
        healthyAgents: [],
        systemStatus: 'critical',
        recommendations: ['No agents registered']
      };
    }

    const healthScores = allMetrics.map(m => this.calculateHealthScore(m));
    const overallHealth = healthScores.reduce((sum, score) => sum + score, 0) / healthScores.length;

    const criticalAgents = allMetrics
      .filter(m => this.calculateHealthScore(m) < 40)
      .map(m => m.agentId);

    const healthyAgents = allMetrics
      .filter(m => this.calculateHealthScore(m) > 70)
      .map(m => m.agentId);

    let systemStatus: 'healthy' | 'degraded' | 'critical';
    if (overallHealth > 70) systemStatus = 'healthy';
    else if (overallHealth > 40) systemStatus = 'degraded';
    else systemStatus = 'critical';

    const recommendations: string[] = [];
    if (criticalAgents.length > 0) {
      recommendations.push(`Address critical health issues in: ${criticalAgents.join(', ')}`);
    }
    if (overallHealth < 60) {
      recommendations.push('Consider system-wide performance optimization');
    }
    if (this.alerts.filter(a => a.severity === 'red').length > 0) {
      recommendations.push('Address high-priority alerts immediately');
    }

    return {
      overallHealth,
      criticalAgents,
      healthyAgents,
      systemStatus,
      recommendations
    };
  }

  private generateHealthReport(analysis: any): string {
    return `[AGENT STATE MONITOR REPORT]

SYSTEM HEALTH OVERVIEW:
- Overall Health: ${analysis.overallHealth.toFixed(1)}%
- System Status: ${analysis.systemStatus.toUpperCase()}
- Active Agents: ${this.healthMetrics.size}
- Active Alerts: ${this.alerts.length}

AGENT STATUS:
${Array.from(this.healthMetrics.values()).map(metrics => {
  const health = this.calculateHealthScore(metrics);
  const status = health > 70 ? '🟢' : health > 40 ? '🟡' : '🔴';
  return `${status} ${metrics.agentId}: ${health.toFixed(1)}% (${metrics.totalTasks} tasks, ${(metrics.errorRate * 100).toFixed(1)}% error rate)`;
}).join('\n')}

CRITICAL AGENTS:
${analysis.criticalAgents.length > 0 ? analysis.criticalAgents.map(id => `- ${id}`).join('\n') : 'None'}

RECENT ALERTS:
${this.alerts.slice(-5).map(alert => 
  `${alert.severity === 'red' ? '🚨' : '⚠️'} ${alert.agentId}: ${alert.message}`
).join('\n')}

RECOMMENDATIONS:
${analysis.recommendations.map((rec: string) => `- ${rec}`).join('\n')}

PERFORMANCE METRICS:
- Average Response Time: ${(Array.from(this.healthMetrics.values()).reduce((sum, m) => sum + m.averageResponseTime, 0) / this.healthMetrics.size / 1000).toFixed(2)}s
- Average Error Rate: ${(Array.from(this.healthMetrics.values()).reduce((sum, m) => sum + m.errorRate, 0) / this.healthMetrics.size * 100).toFixed(1)}%
- Total Tasks Processed: ${Array.from(this.healthMetrics.values()).reduce((sum, m) => sum + m.totalTasks, 0)}

[END HEALTH REPORT]`;
  }

  private generateHealthSuggestions(analysis: any): string[] {
    const suggestions: string[] = [];

    if (analysis.systemStatus === 'critical') {
      suggestions.push('Immediate system attention required - critical performance issues detected');
    }

    if (analysis.criticalAgents.length > 0) {
      suggestions.push(`Prioritize fixing critical agents: ${analysis.criticalAgents.join(', ')}`);
    }

    if (this.alerts.filter((a: HealthAlert) => a.severity === 'red').length > 2) {
      suggestions.push('Multiple critical alerts - consider system-wide intervention');
    }

    suggestions.push('Regularly review agent performance trends');
    suggestions.push('Consider load balancing if response times are consistently high');
    suggestions.push('Update agent configurations based on performance data');

    return suggestions;
  }

  public destroy(): void {
    this.stopMonitoring();
    this.healthMetrics.clear();
    this.healthHistory.clear();
    this.alerts.length = 0;
    this.alertListeners.length = 0;
  }
}