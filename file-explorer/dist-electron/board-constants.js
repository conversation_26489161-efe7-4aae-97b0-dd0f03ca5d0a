"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BOARD_EVENTS = exports.BOARD_COMMANDS = void 0;
// Board IPC Commands
exports.BOARD_COMMANDS = {
    GET_STATE: 'board:get-state',
    CREATE_CARD: 'board:create-card',
    UPDATE_CARD: 'board:update-card',
    DELETE_CARD: 'board:delete-card',
    MOVE_CARD: 'board:move-card',
    UPDATE_COLUMN: 'board:update-column',
    UPDATE_BOARD: 'board:update-board',
};
// Board IPC Events
exports.BOARD_EVENTS = {
    STATE_UPDATE: 'board:state-update',
    CARD_CREATED: 'board:card-created',
    CARD_UPDATED: 'board:card-updated',
    CARD_DELETED: 'board:card-deleted',
    CARD_MOVED: 'board:card-moved',
    COLUMN_UPDATED: 'board:column-updated',
    AGENT_STATUS: 'board:agent-status',
    AGENT_LOG: 'board:agent-log',
};
