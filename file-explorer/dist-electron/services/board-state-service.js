"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BoardStateService = void 0;
const electron_1 = require("electron");
const board_constants_1 = require("../board-constants");
// Global board state storage
const boardStates = new Map();
class BoardStateService {
    constructor() {
        this.windows = new Set();
        this.registerIPCHandlers();
    }
    // Register a window to receive board state updates
    registerWindow(window) {
        this.windows.add(window);
        // Clean up when window is closed
        window.on('closed', () => {
            this.windows.delete(window);
        });
    }
    // Broadcast state update to all registered windows
    broadcastStateUpdate(boardId, state) {
        this.windows.forEach(window => {
            if (!window.isDestroyed()) {
                window.webContents.send(board_constants_1.BOARD_EVENTS.STATE_UPDATE, state);
            }
        });
    }
    // Broadcast specific events to all windows
    broadcastCardCreated(card) {
        this.windows.forEach(window => {
            if (!window.isDestroyed()) {
                window.webContents.send(board_constants_1.BOARD_EVENTS.CARD_CREATED, card);
            }
        });
    }
    broadcastCardUpdated(card) {
        this.windows.forEach(window => {
            if (!window.isDestroyed()) {
                window.webContents.send(board_constants_1.BOARD_EVENTS.CARD_UPDATED, card);
            }
        });
    }
    broadcastCardDeleted(cardId, columnId) {
        this.windows.forEach(window => {
            if (!window.isDestroyed()) {
                window.webContents.send(board_constants_1.BOARD_EVENTS.CARD_DELETED, cardId, columnId);
            }
        });
    }
    broadcastCardMoved(cardId, sourceColumnId, targetColumnId, swimlaneId) {
        this.windows.forEach(window => {
            if (!window.isDestroyed()) {
                window.webContents.send(board_constants_1.BOARD_EVENTS.CARD_MOVED, cardId, sourceColumnId, targetColumnId, swimlaneId);
            }
        });
    }
    // Initialize board state if it doesn't exist
    initializeBoardIfNeeded(boardId) {
        if (!boardStates.has(boardId)) {
            const defaultBoard = {
                id: boardId,
                name: boardId === 'main' ? 'Main Board' : `Board ${boardId}`,
                columns: [
                    { id: 'backlog', title: 'Backlog', cards: [] },
                    { id: 'todo', title: 'To Do', cards: [] },
                    { id: 'in-progress', title: 'In Progress', cards: [] },
                    { id: 'review', title: 'Review', cards: [] },
                    { id: 'done', title: 'Done', cards: [] },
                ],
                swimlanes: [
                    { id: 'default', title: 'Default', isExpanded: true },
                ],
                cardTypes: [],
                agents: [],
            };
            boardStates.set(boardId, defaultBoard);
        }
        return boardStates.get(boardId);
    }
    registerIPCHandlers() {
        // Get board state
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.GET_STATE, (_, boardId) => {
            return this.initializeBoardIfNeeded(boardId);
        });
        // Create card
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.CREATE_CARD, (_, boardId, columnId, cardData) => {
            const board = this.initializeBoardIfNeeded(boardId);
            const column = board.columns.find(col => col.id === columnId);
            if (!column) {
                throw new Error(`Column ${columnId} not found`);
            }
            const newCard = {
                ...cardData,
                id: `card-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                columnId,
            };
            column.cards.push(newCard);
            // Broadcast to all windows
            this.broadcastCardCreated(newCard);
            this.broadcastStateUpdate(boardId, board);
            return newCard;
        });
        // Update card
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.UPDATE_CARD, (_, boardId, cardId, updates) => {
            const board = this.initializeBoardIfNeeded(boardId);
            // Find the card across all columns
            let targetCard = null;
            for (const column of board.columns) {
                const card = column.cards.find(c => c.id === cardId);
                if (card) {
                    targetCard = card;
                    Object.assign(card, updates);
                    break;
                }
            }
            if (!targetCard) {
                throw new Error(`Card ${cardId} not found`);
            }
            // Broadcast to all windows
            this.broadcastCardUpdated(targetCard);
            this.broadcastStateUpdate(boardId, board);
            return targetCard;
        });
        // Delete card
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.DELETE_CARD, (_, boardId, columnId, cardId) => {
            const board = this.initializeBoardIfNeeded(boardId);
            const column = board.columns.find(col => col.id === columnId);
            if (!column) {
                throw new Error(`Column ${columnId} not found`);
            }
            const cardIndex = column.cards.findIndex(card => card.id === cardId);
            if (cardIndex === -1) {
                throw new Error(`Card ${cardId} not found in column ${columnId}`);
            }
            column.cards.splice(cardIndex, 1);
            // Broadcast to all windows
            this.broadcastCardDeleted(cardId, columnId);
            this.broadcastStateUpdate(boardId, board);
            return true;
        });
        // Move card
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.MOVE_CARD, (_, boardId, cardId, sourceColumnId, targetColumnId, swimlaneId, targetIndex) => {
            const board = this.initializeBoardIfNeeded(boardId);
            const sourceColumn = board.columns.find(col => col.id === sourceColumnId);
            const targetColumn = board.columns.find(col => col.id === targetColumnId);
            if (!sourceColumn || !targetColumn) {
                throw new Error('Source or target column not found');
            }
            const cardIndex = sourceColumn.cards.findIndex(card => card.id === cardId);
            if (cardIndex === -1) {
                throw new Error(`Card ${cardId} not found in source column`);
            }
            const [card] = sourceColumn.cards.splice(cardIndex, 1);
            card.columnId = targetColumnId;
            card.swimlaneId = swimlaneId;
            // Insert at specific index or at the end
            if (typeof targetIndex === 'number' && targetIndex >= 0) {
                targetColumn.cards.splice(targetIndex, 0, card);
            }
            else {
                targetColumn.cards.push(card);
            }
            // Broadcast to all windows
            this.broadcastCardMoved(cardId, sourceColumnId, targetColumnId, swimlaneId);
            this.broadcastStateUpdate(boardId, board);
            return card;
        });
        // Update column
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.UPDATE_COLUMN, (_, boardId, columnId, updates) => {
            const board = this.initializeBoardIfNeeded(boardId);
            const column = board.columns.find(col => col.id === columnId);
            if (!column) {
                throw new Error(`Column ${columnId} not found`);
            }
            Object.assign(column, updates);
            // Broadcast to all windows
            this.broadcastStateUpdate(boardId, board);
            return column;
        });
        // Update board
        electron_1.ipcMain.handle(board_constants_1.BOARD_COMMANDS.UPDATE_BOARD, (_, boardId, updates) => {
            const board = this.initializeBoardIfNeeded(boardId);
            Object.assign(board, updates);
            // Broadcast to all windows
            this.broadcastStateUpdate(boardId, board);
            return board;
        });
    }
}
exports.BoardStateService = BoardStateService;
