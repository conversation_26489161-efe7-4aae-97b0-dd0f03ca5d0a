// lib/agent-ipc-bridge.ts
import {
  AGENT_COMMANDS,
  AGENT_EVENTS,
  AgentStatus,
  AgentMessage,
  AgentTask,
  AgentSystemState
} from './agent-constants';

// Re-export types for convenience
export type {
  AgentStatus,
  AgentMessage,
  AgentTask,
  AgentSystemState
};

export interface AgentEventListeners {
  onStateUpdate?: (state: AgentSystemState) => void;
  onMessageAdded?: (message: AgentMessage) => void;
  onAgentStatusChanged?: (agentId: string, status: AgentStatus) => void;
  onTaskAssigned?: (task: AgentTask) => void;
  onRunningStateChanged?: (isRunning: boolean) => void;
}

export class AgentIPCBridge {
  private eventListeners: AgentEventListeners = {};

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      // Listen for state updates from main process
      window.electronAPI.ipcRenderer.on(AGENT_EVENTS.STATE_UPDATE, (_, state: AgentSystemState) => {
        this.eventListeners.onStateUpdate?.(state);
      });

      window.electronAPI.ipcRenderer.on(AGENT_EVENTS.MESSAGE_ADDED, (_, message: AgentMessage) => {
        this.eventListeners.onMessageAdded?.(message);
      });

      window.electronAPI.ipcRenderer.on(AGENT_EVENTS.AGENT_STATUS_CHANGED, (_, agentId: string, status: AgentStatus) => {
        this.eventListeners.onAgentStatusChanged?.(agentId, status);
      });

      window.electronAPI.ipcRenderer.on(AGENT_EVENTS.TASK_ASSIGNED, (_, task: AgentTask) => {
        this.eventListeners.onTaskAssigned?.(task);
      });

      window.electronAPI.ipcRenderer.on(AGENT_EVENTS.RUNNING_STATE_CHANGED, (_, isRunning: boolean) => {
        this.eventListeners.onRunningStateChanged?.(isRunning);
      });
    }
  }

  public registerEventListeners(listeners: AgentEventListeners): () => void {
    this.eventListeners = { ...this.eventListeners, ...listeners };

    return () => {
      this.eventListeners = {};
    };
  }

  // Agent state operations
  public async getState(): Promise<AgentSystemState> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(AGENT_COMMANDS.GET_STATE);
    }
    // Return default state when Electron API is not available
    return {
      agents: [
        {
          id: 'micromanager',
          name: 'AI Agent Orchestrator',
          type: 'orchestrator',
          status: 'idle',
          healthScore: 100,
          tokensUsed: 0,
          capabilities: ['task_decomposition', 'agent_coordination', 'project_orchestration']
        }
      ],
      messages: [],
      tasks: [],
      isRunning: false,
      selectedAgent: 'micromanager'
    };
  }

  public async updateAgentStatus(
    agentId: string,
    status: AgentStatus['status'],
    healthScore?: number,
    tokensUsed?: number
  ): Promise<AgentStatus | null> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(
        AGENT_COMMANDS.UPDATE_AGENT_STATUS,
        agentId,
        status,
        healthScore,
        tokensUsed
      );
    }
    // Return null when Electron API is not available
    return null;
  }

  public async addMessage(message: Omit<AgentMessage, 'id'>): Promise<AgentMessage> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(AGENT_COMMANDS.ADD_MESSAGE, message);
    }
    // Return mock message when Electron API is not available
    return {
      ...message,
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
  }

  public async assignTask(task: Omit<AgentTask, 'id' | 'createdAt' | 'updatedAt'>): Promise<AgentTask> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(AGENT_COMMANDS.ASSIGN_TASK, task);
    }
    // Return mock task when Electron API is not available
    return {
      ...task,
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  public async updateTask(taskId: string, updates: Partial<AgentTask>): Promise<AgentTask | null> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(AGENT_COMMANDS.UPDATE_TASK, taskId, updates);
    }
    // Return null when Electron API is not available
    return null;
  }

  public async clearMessages(): Promise<boolean> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(AGENT_COMMANDS.CLEAR_MESSAGES);
    }
    // Return true when Electron API is not available
    return true;
  }

  public async setRunningState(isRunning: boolean): Promise<boolean> {
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.ipcRenderer) {
      return await window.electronAPI.ipcRenderer.invoke(AGENT_COMMANDS.SET_RUNNING_STATE, isRunning);
    }
    // Return the input value when Electron API is not available
    return isRunning;
  }
}

// Export singleton instance
export const agentIPCBridge = new AgentIPCBridge();
