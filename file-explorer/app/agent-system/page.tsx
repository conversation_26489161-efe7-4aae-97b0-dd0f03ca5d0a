"use client"

import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import { CompleteAgentSystem } from '@/components/agents/complete-integration';

export default function AgentSystemWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="h-screen w-full bg-background text-foreground">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <h1 className="text-lg font-semibold">Agent System</h1>
          </div>
          
          {/* Agent System Content */}
          <div className="flex-1 overflow-hidden">
            <CompleteAgentSystem />
          </div>
        </div>
        <Toaster />
      </div>
    </ThemeProvider>
  );
}
