"use client"

import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import AiChatPanel from "@/components/ai-chat-panel";

export default function ChatWindowPage() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <div className="h-screen w-full bg-background text-foreground">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <h1 className="text-lg font-semibold">AI Chat</h1>
          </div>
          
          {/* Chat Content */}
          <div className="flex-1 overflow-hidden">
            <AiChatPanel onClose={() => {}} />
          </div>
        </div>
        <Toaster />
      </div>
    </ThemeProvider>
  );
}
