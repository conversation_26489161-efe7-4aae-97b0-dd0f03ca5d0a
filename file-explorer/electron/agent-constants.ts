// electron/agent-constants.ts
// Shared constants for agent system IPC communication

export const AGENT_COMMANDS = {
  GET_STATE: 'agent:get-state',
  UPDATE_AGENT_STATUS: 'agent:update-agent-status',
  ADD_MESSAGE: 'agent:add-message',
  ASSIGN_TASK: 'agent:assign-task',
  UPDATE_TASK: 'agent:update-task',
  CLEAR_MESSAGES: 'agent:clear-messages',
  SET_RUNNING_STATE: 'agent:set-running-state',
} as const;

export const AGENT_EVENTS = {
  STATE_UPDATE: 'agent:state-update',
  MESSAGE_ADDED: 'agent:message-added',
  AGENT_STATUS_CHANGED: 'agent:agent-status-changed',
  TASK_ASSIGNED: 'agent:task-assigned',
  RUNNING_STATE_CHANGED: 'agent:running-state-changed',
} as const;

// Agent state interfaces
export interface AgentStatus {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'busy' | 'error' | 'paused';
  healthScore: number;
  tokensUsed: number;
  currentTask?: string;
  capabilities: string[];
}

export interface AgentMessage {
  id: string;
  agentId: string;
  message: string;
  timestamp: number;
  type: 'info' | 'error' | 'success' | 'warning';
}

export interface AgentTask {
  id: string;
  agentId: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  createdAt: number;
  updatedAt: number;
}

export interface AgentSystemState {
  agents: AgentStatus[];
  messages: AgentMessage[];
  tasks: AgentTask[];
  isRunning: boolean;
  selectedAgent: string;
}
