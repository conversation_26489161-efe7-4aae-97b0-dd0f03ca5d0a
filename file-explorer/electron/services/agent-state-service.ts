// electron/services/agent-state-service.ts
import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import {
  AGENT_COMMANDS,
  AGENT_EVENTS,
  AgentStatus,
  AgentMessage,
  AgentTask,
  AgentSystemState
} from '../agent-constants';

export class AgentStateService {
  private registeredWindows = new Set<BrowserWindow>();
  private agentState: AgentSystemState = {
    agents: [
      {
        id: 'micromanager',
        name: 'AI Agent Orchestrator',
        type: 'orchestrator',
        status: 'idle',
        healthScore: 100,
        tokensUsed: 0,
        capabilities: ['task_decomposition', 'agent_coordination', 'project_orchestration']
      },
      {
        id: 'intern',
        name: 'Intern Agent',
        type: 'intern',
        status: 'idle',
        healthScore: 100,
        tokensUsed: 0,
        capabilities: ['basic_tasks', 'code_review', 'documentation']
      },
      {
        id: 'senior',
        name: 'Senior Agent',
        type: 'senior',
        status: 'idle',
        healthScore: 100,
        tokensUsed: 0,
        capabilities: ['complex_tasks', 'architecture', 'optimization']
      },
      {
        id: 'architect',
        name: 'Architect Agent',
        type: 'architect',
        status: 'idle',
        healthScore: 100,
        tokensUsed: 0,
        capabilities: ['system_design', 'architecture_review', 'technical_leadership']
      },
      {
        id: 'designer',
        name: 'Designer Agent',
        type: 'designer',
        status: 'idle',
        healthScore: 100,
        tokensUsed: 0,
        capabilities: ['ui_design', 'ux_optimization', 'visual_design']
      },
      {
        id: 'tester',
        name: 'Tester Agent',
        type: 'tester',
        status: 'idle',
        healthScore: 100,
        tokensUsed: 0,
        capabilities: ['test_creation', 'quality_assurance', 'bug_detection']
      }
    ],
    messages: [],
    tasks: [],
    isRunning: false,
    selectedAgent: 'micromanager'
  };

  constructor() {
    this.registerIPCHandlers();
    console.log('AgentStateService: Initialized');
  }

  public registerWindow(window: BrowserWindow) {
    this.registeredWindows.add(window);
    window.on('closed', () => {
      this.registeredWindows.delete(window);
      console.log('AgentStateService: Window unregistered');
    });
    console.log('AgentStateService: Window registered');
  }

  private broadcastStateUpdate() {
    this.registeredWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send(AGENT_EVENTS.STATE_UPDATE, this.agentState);
      }
    });
  }

  private registerIPCHandlers() {
    ipcMain.handle(AGENT_COMMANDS.GET_STATE, () => {
      console.log('IPC: GET_STATE for agent system');
      return this.agentState;
    });

    ipcMain.handle(AGENT_COMMANDS.UPDATE_AGENT_STATUS, (_, agentId: string, status: AgentStatus['status'], healthScore?: number, tokensUsed?: number) => {
      const agent = this.agentState.agents.find(a => a.id === agentId);
      if (agent) {
        agent.status = status;
        if (healthScore !== undefined) agent.healthScore = healthScore;
        if (tokensUsed !== undefined) agent.tokensUsed = tokensUsed;
        this.broadcastStateUpdate();
        return agent;
      }
      return null;
    });

    ipcMain.handle(AGENT_COMMANDS.ADD_MESSAGE, (_, message: Omit<AgentMessage, 'id'>) => {
      const newMessage: AgentMessage = {
        ...message,
        id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };
      this.agentState.messages.push(newMessage);
      // Keep only last 100 messages
      if (this.agentState.messages.length > 100) {
        this.agentState.messages = this.agentState.messages.slice(-100);
      }
      this.broadcastStateUpdate();
      return newMessage;
    });

    ipcMain.handle(AGENT_COMMANDS.ASSIGN_TASK, (_, task: Omit<AgentTask, 'id' | 'createdAt' | 'updatedAt'>) => {
      const newTask: AgentTask = {
        ...task,
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      this.agentState.tasks.push(newTask);

      // Update agent status
      const agent = this.agentState.agents.find(a => a.id === task.agentId);
      if (agent) {
        agent.status = 'busy';
        agent.currentTask = newTask.id;
      }

      this.broadcastStateUpdate();
      return newTask;
    });

    ipcMain.handle(AGENT_COMMANDS.UPDATE_TASK, (_, taskId: string, updates: Partial<AgentTask>) => {
      const task = this.agentState.tasks.find(t => t.id === taskId);
      if (task) {
        Object.assign(task, updates, { updatedAt: Date.now() });

        // If task is completed or failed, update agent status
        if (updates.status === 'completed' || updates.status === 'failed') {
          const agent = this.agentState.agents.find(a => a.currentTask === taskId);
          if (agent) {
            agent.status = 'idle';
            agent.currentTask = undefined;
          }
        }

        this.broadcastStateUpdate();
        return task;
      }
      return null;
    });

    ipcMain.handle(AGENT_COMMANDS.CLEAR_MESSAGES, () => {
      this.agentState.messages = [];
      this.broadcastStateUpdate();
      return true;
    });

    ipcMain.handle(AGENT_COMMANDS.SET_RUNNING_STATE, (_, isRunning: boolean) => {
      this.agentState.isRunning = isRunning;
      this.broadcastStateUpdate();
      return isRunning;
    });

    console.log('AgentStateService: IPC Handlers Registered');
  }
}
